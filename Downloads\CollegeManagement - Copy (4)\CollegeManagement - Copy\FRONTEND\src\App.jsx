// import { useState } from 'react'
// import './App.css'

// import EventList from './Components/eventlist'

// function App() {
//     return<EventList />
// }

// export default App;


import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import './App.css';

import EventList from './Components/eventlist';
import EventUpdate from './Components/eventupdate'; 
import EventDetails from './Components/EventDetails';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<EventList />} />   
        <Route path="/events" element={<EventList />} /> 
        <Route path="/events/update/:id" element={<EventUpdate />} /> 
        <Route path="/events/:id" element={<EventDetails />} /> 
      </Routes>
    </Router>
  );
}

export default App;
