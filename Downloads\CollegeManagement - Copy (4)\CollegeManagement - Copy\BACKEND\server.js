const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');

const eventroutes=require('./routes/eventroutes')

const app = express();
const PORT = 5000;

// Middleware
app.use(cors());
app.use(express.json());

// // Path to JSON file where events are stored
// const eventsFile = path.join(__dirname, 'events.json');

// // Function to read events from file
// function readEvents() {
//     if (!fs.existsSync(eventsFile)) {
//         fs.writeFileSync(eventsFile, JSON.stringify([]));
//     }
//     const data = fs.readFileSync(eventsFile);
//     return JSON.parse(data);
// }

// // Function to write events to file
// function writeEvents(events) {
//     fs.writeFileSync(eventsFile, JSON.stringify(events, null, 2));
// }

app.get('/',(req,res)=>{
    res.send("college event management system backend running");
})

// GET all events
// app.get('/events', (req, res) => {
//     const events = readEvents();
//     res.json(events);
// });

// // POST a new event
// app.post('/events', (req, res) => {
//     const { name, date } = req.body;

//     if (!name || !date) {
//         return res.status(400).json({ error: 'Name and date are required' });
//     }

//     const events = readEvents();
//     const newEvent = {
//         id: events.length ? events[events.length - 1].id + 1 : 1,
//         name,
//         date
//     };

//     events.push(newEvent);
//     writeEvents(events);

//     res.status(201).json(newEvent);
// });

app.use('/events',eventroutes);

// Start server
app.listen(PORT, () => {
    console.log(`Server running at http://localhost:${PORT}`);
});
