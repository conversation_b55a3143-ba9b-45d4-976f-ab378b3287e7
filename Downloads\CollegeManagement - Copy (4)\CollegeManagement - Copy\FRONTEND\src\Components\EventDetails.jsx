// src/Components/EventDetails.js
import React, { useEffect, useState } from "react";
import { useParams, Link } from "react-router-dom";

const EventDetails = () => {
  const { id } = useParams(); // Get id from URL
  const [event, setEvent] = useState(null);

  useEffect(() => {
    fetch(`http://localhost:5000/events/${id}`)
      .then((res) => {
        if (!res.ok) throw new Error("Event not found");
        return res.json();
      })
      .then((data) => setEvent(data))
      .catch((err) => console.error(err));
  }, [id]);

  if (!event) return <p>Loading event...</p>;

  return (
    <div>
      <h2>Event Details</h2>
      <p><strong>Name:</strong> {event.name}</p>
      <p><strong>Date:</strong> {event.date}</p>

      <Link to="/">Back to Events</Link>
    </div>
  );
};

export default EventDetails;
