import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import "./eventroutes.css";

const EventUpdate = () => {
  const { id } = useParams(); 
  const navigate = useNavigate();
  const [formData, setFormData] = useState({ name: "", date: "" });

  // fetch event by id
  useEffect(() => {
    fetch(`http://localhost:5000/events/${id}`)
      .then((res) => res.json())
      .then((data) => setFormData({ name: data.name, date: data.date }))
      .catch((err) => console.error("Error fetching event:", err));
  }, [id]);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  // update event
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await fetch(`http://localhost:5000/events/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });
      navigate("/events"); 
    } catch (err) {
      console.error("Error updating event:", err);
    }
  };

  return (
    <div className="container">
      <h1 className="heading">Update Event</h1>

      <form className="form" onSubmit={handleSubmit}>
        <input className="input" type="text" name="name" placeholder="Event Name" value={formData.name} onChange={handleChange} required />
        <input className="input" type="date" name="date" value={formData.date} onChange={handleChange} required/>
        <button className="button" type="submit">
          Update Event
        </button>
      </form>
    </div>
  );
};

export default EventUpdate;
