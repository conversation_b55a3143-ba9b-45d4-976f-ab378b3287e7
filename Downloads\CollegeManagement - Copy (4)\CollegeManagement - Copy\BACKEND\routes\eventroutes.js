const express = require('express');
const router = express.Router();
// let events = require('../data/event');

const fs=require('fs');

const path=require('path');
// const { fileURLToPath } = require('url');
const filePath=path.join(__dirname,"../data/events.json");
function getEvents(){
    if(!fs.existsSync(filePath)){
        return [];
    }

    const data=fs.readFileSync(filePath,'utf-8');
    return JSON.parse(data||"[]")
}
function saveEvents(events){
    // if(!fs.existsSync(filePath)){
        
    fs.writeFileSync(filePath,JSON.stringify(events,null,2),"utf-8");
    // }
}
//GET ALL EVENTS
// router.get('/',(req,res)=>{
//     try {
//         res.json(events);
//     } 
//     catch (err) {
//         res.status(500).json({error:"Something went wrong while fetching events"});
//     }
// });

router.get('/',(req,res)=>{
    try{

        let events=getEvents();
        

        //SEARCH BY NAMES
        if(req.query.name){
            const name=req.query.name.toLowerCase();
            events=events.filter(e=>e.name.toLowerCase().include(name));
        }

        //FILTER BY DATE
        if(req.query.date){
            events=events.filter(e=>e.date===req.query.date);
        }
        // const events=getEvents();
        res.json(events);
    }
    catch(err){
        console.log(err);
        res.status(500).json({error:"something went wrong"})
    }
})


//GET THE EVENT BY ID
// router.get('/:id',(req,res)=>{
//     try{
//         const event = events.find(e=>e.id===parseInt(req.params.id));

//         if (!event) {
//             return res.status(404).send("Event not found");
//         }

//         res.json(event);
//     } 
//     catch(err) {
//         res.status(500).json({ error: "Something went wrong while fetching the event" });
//     }
// });

router.get('/:id', (req, res) => {
    try {
        const events = getEvents();
        const event = events.find(e => e.id === parseInt(req.params.id));

        if (!event) {
            return res.status(404).send("Event not found");
        }
        res.json(event);
    } catch (err) {
        res.status(500).json({ error: "Something went wrong while fetching the event" });
    }
});

//ADD A NEW EVENT
// router.post('/',(req,res)=>{
//     try{
//         const newEvent = {
//             id: events.length + 1,
//             name: req.body.name,
//             date: req.body.date
//         };

//         events.push(newEvent);
//         res.status(201).json(newEvent);
//     } 
//     catch(err){
//         res.status(500).json({error:"Something went wrong while adding the event"});
//     }
// });

router.post('/', (req, res) => {
    try {
        const events = getEvents();
        const newEvent = {
            id: events.length ? events[events.length - 1].id + 1 : 1,
            name: req.body.name,
            date: req.body.date
        };

        events.push(newEvent);
        saveEvents(events);

        res.status(201).json(newEvent);
    } catch (err) {
        res.status(500).json({ error: "Something went wrong while adding the event" });
    }
});


//UPDATE EVENTS
router.put('/:id', (req, res) => {
    try {
        const events = getEvents();
        const event = events.find(e => e.id === parseInt(req.params.id));

        if (!event) {
            return res.status(404).send('Event not found');
        }

        event.name = req.body.name || event.name;
        event.date = req.body.date || event.date;

        saveEvents(events);
        res.json(event);
    } catch (err) {
        res.status(500).json({ error: "Something went wrong while updating the event" });
    }
});

router.delete('/:id', (req, res) => {
    try {
        let events = getEvents();
        const index = events.findIndex(e => e.id === parseInt(req.params.id));

        if (index === -1) {
            return res.status(404).send('Event not found');
        }

        const [deleted] = events.splice(index, 1);
        saveEvents(events);

        res.json(deleted);
    } catch (err) {
        res.status(500).json({ error: "Something went wrong while deleting the event" });
    }
});

module.exports=router;
