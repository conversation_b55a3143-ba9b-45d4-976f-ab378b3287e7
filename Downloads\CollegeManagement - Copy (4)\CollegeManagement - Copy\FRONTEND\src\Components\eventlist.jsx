// import React, { useEffect, useState } from 'react';

// const EventList = () => {
//     const [events, setEvents] = useState([]);
//     const [loading, setLoading] = useState(true);
//     const [formData, setFormData] = useState({ name: '', date: '' });

//   const styles = {
//     container: {
//         padding: '20px',
//         textAlign: 'center',
//         fontFamily: 'Arial, sans-serif',
//         maxWidth: '600px',
//         margin: '0 auto',
//         backgroundColor: '#121212',
//         color: '#fff', 
//         borderRadius: '12px',
//         boxShadow: '0px 4px 20px rgba(0,0,0,0.4)',
//         transition: 'all 0.3s ease-in-out'
//     },
//     heading: {
//         marginBottom: '20px',
//         fontSize: '2rem',
//         fontWeight: 'bold',
//         letterSpacing: '1px'
//     },
//     card: {
//         background: '#1e1e1e',
//         margin: '10px auto',
//         padding: '15px',
//         borderRadius: '10px',
//         boxShadow: '0px 4px 12px rgba(0,0,0,0.3)',
//         transition: 'transform 0.2s ease-in-out',
//     },
//     cardHover: {
//         transform: 'scale(1.02)',
//     },
//     title: {
//         margin: 0,
//         color: '#fff',
//     },
//     date: {
//         color: '#bbb',
//     },
//     form: {
//         display: 'flex',
//         flexDirection: 'column',
//         gap: '12px',
//         marginBottom: '20px',
//         animation: 'fadeIn 0.5s ease-in-out',
//     },
//     input: {
//         padding: '12px',
//         borderRadius: '5px',
//         border: '1px solid #333',
//         fontSize: '16px',
//         backgroundColor: '#1e1e1e',
//         color: '#fff',
//         outline: 'none',
//         transition: 'border-color 0.3s ease',
//     },
//     inputFocus: {
//         borderColor: '#00ff88',
//     },
//     button: {
//         padding: '12px',
//         borderRadius: '5px',
//         border: 'none',
//         background: 'linear-gradient(90deg, #00ff88, #00b3ff)',
//         color: '#121212',
//         fontSize: '16px',
//         cursor: 'pointer',
//         fontWeight: 'bold',
//         letterSpacing: '0.5px',
//         transition: 'transform 0.2s ease, box-shadow 0.2s ease',
//     },
//     buttonHover: {
//         transform: 'scale(1.05)',
//         boxShadow: '0px 4px 15px rgba(200, 200, 200, 0.5)',
//     },
//     buttonDisabled: {
//         background: '#313131ff',
//         color: '#888',
//         cursor: 'not-allowed',
//     }
// };


//     const fetchEvents = () => {
//         setLoading(true);
//         fetch('http://localhost:5000/events')
//             .then(res => res.json())
//             .then(data => {
//                 setEvents(data);
//                 setLoading(false);
//             })
//             .catch(err => {
//                 console.error('Error fetching events:', err);
//                 setLoading(false);
//             });
//     };

//     useEffect(() => {
//         fetchEvents();
//     }, []);

//     const handleChange = (e) => {
//         setFormData({
//             ...formData,
//             [e.target.name]: e.target.value
//         });
//     };

//     const handleSubmit = (e) => {
//         e.preventDefault();

//         fetch('http://localhost:5000/events', {
//             method: 'POST',
//             headers: { 'Content-Type': 'application/json' },
//             body: JSON.stringify(formData),
//         })
//             .then(res => res.json())
//             .then(data => {
//                 setFormData({ name: '', date: '' }); 
//                 fetchEvents(); 
//             })
//             .catch(err => console.error('Error adding event:', err));
//     };

//     return (
//         <div style={styles.container}>
//             <h1 style={styles.heading}>Upcoming Events</h1>

//             {}
//             <form style={styles.form} onSubmit={handleSubmit}>
//                 <input
//                     style={styles.input}
//                     type="text"
//                     name="name"
//                     placeholder="Event Name"
//                     value={formData.name}
//                     onChange={handleChange}
//                     required
//                 />
//                 <input
//                     style={styles.input}
//                     type="date"
//                     name="date"
//                     value={formData.date}
//                     onChange={handleChange}
//                     required
//                 />
//                 <button
//                     style={{
//                         ...styles.button,
//                         ...(formData.name && formData.date ? {} : styles.buttonDisabled)
//                     }}
//                     type="submit"
//                     disabled={!formData.name || !formData.date}
//                 >
//                     Add Event
//                 </button>
//             </form>

//             {/* Event List */}
//             {loading ? (
//                 <p>Loading events...</p>
//             ) : events.length === 0 ? (
//                 <p>No events available.</p>
//             ) : (
//                 events.map(event => (
//                     <div key={event.id} style={styles.card}>
//                         <h2 style={styles.title}>{event.name}</h2>
//                         <p style={styles.date}>Date: {event.date}</p>
//                     </div>
//                 ))
//             )}
//         </div>
//     );
// };

// export default EventList;


// import React, { useEffect, useState } from "react";
// import { Link } from "react-router-dom";
// import "./eventroutes.css";

// const EventList = () => {
//     const [events, setEvents] = useState([]);
//     const [formData, setFormData] = useState({ name: '', date: '' });

//     const fetchEvents = () => {
//         fetch('http://localhost:5000/events')
//             .then(res => res.json())
//             .then(data => setEvents(data))
//             .catch(err => console.error('Error fetching events:', err));
//     };
//     useEffect(() => {
//         fetchEvents();
//     }, []);

//     const handleChange = (e) => {
//         setFormData({ ...formData, [e.target.name]: e.target.value });
//     };  
//     const handleSubmit=async(e)=>{
//     e.preventDefault();
//     try {
//         await fetch('http://localhost:5000/events', {
//             method: 'POST',
//             headers: { 'Content-Type': 'application/json' },
//             body: JSON.stringify(formData),
//         });
//         setFormData({ name: '', date: '' });
//         fetchEvents();
//     } catch (err) {
//         console.error('Error adding event:', err);
//     }
// };

//     return (
//         <div>


//             <h1>Upcoming Events</h1>

//             <form onSubmit={handleSubmit}>
//                 <input type="text" name="name" placeholder="Event Name" value={formData.name} onChange={handleChange} required/>
//                 <input type="date" name="date" value={formData.date} onChange={handleChange} required />
//                 <button type="submit">Add Event</button>
//             </form>

//            <ul className="list">
//         {events.length === 0 ? (
//           <p>No events available.</p>
//         ) : (
//           events.map((event) => (
//             <li key={event.id} className="listItem">
//               <strong>{event.name}</strong> - {event.date}
//               <Link className="button" to={`/events/update/${event.id}`} > Edit </Link>
//             </li>))
//         )}
//       </ul>

      
//         </div>
//     );
// };

// export default EventList;


// import React, { useEffect, useState } from "react";
// import { Link } from "react-router-dom";
// import "./eventroutes.css";

// const EventList = () => {
//   const [events, setEvents] = useState([]);
//   const [formData, setFormData] = useState({ name: "", date: "" });

//   // Fetch events from backend
//   const fetchEvents = async () => {
//     try {
//       const res = await fetch("http://localhost:5000/events");
//       if (!res.ok) throw new Error("Failed to fetch events");
//       const data = await res.json();

//       // Ensure data is always an array
//       setEvents(Array.isArray(data) ? data : []);
//     } catch (err) {
//       console.error("Error fetching events:", err);
//       setEvents([]); // fallback
//     }
//   };

//   useEffect(() => {
//     fetchEvents();
//   }, []);

//   // Handle input change
//   const handleChange = (e) => {
//     setFormData({ ...formData, [e.target.name]: e.target.value });
//   };

//   // Add new event
//   const handleSubmit = async (e) => {
//     e.preventDefault();
//     try {
//       const res = await fetch("http://localhost:5000/events", {
//         method: "POST",
//         headers: { "Content-Type": "application/json" },
//         body: JSON.stringify(formData),
//       });
//       if (!res.ok) throw new Error("Failed to add event");

//       setFormData({ name: "", date: "" }); // reset form
//       fetchEvents(); // refresh list
//     } catch (err) {
//       console.error("Error adding event:", err);
//       alert("Failed to add event.");
//     }
//   };

//   // Delete event
//   const handleDelete = async (id) => {
//     if (!window.confirm("Delete this event?")) return;
//     try {
//       const res = await fetch(`http://localhost:5000/events/${id}`, {
//         method: "DELETE",
//       });
//       if (!res.ok) throw new Error(await res.text());
//       const deleted = await res.json();

//       // remove deleted event locally
//       setEvents((prev) => prev.filter((e) => e.id !== deleted.id));
//     } catch (err) {
//       console.error("Error deleting event:", err);
//       alert("Failed to delete event.");
//     }
//   };

//   return (
//     <div>
//       <h1>Upcoming Events</h1>

//       {/* Add Event Form */}
//       <form onSubmit={handleSubmit}>
//         <input
//           type="text"
//           name="name"
//           placeholder="Event Name"
//           value={formData.name}
//           onChange={handleChange}
//           required
//         />
//         <input
//           type="date"
//           name="date"
//           value={formData.date}
//           onChange={handleChange}
//           required
//         />
//         <button type="submit">Add Event</button>
//       </form>

//       {/* Event List */}
//       <ul className="list">
//         {events.length === 0 ? (
//           <p>No events available.</p>
//         ) : (
//           events.map((event) => (
//             <li key={event.id} className="listItem">
//               <strong>{event.name}</strong> - {event.date}
//               <Link className="button" to={`/events/update/${event.id}`}>
//                 Edit
//               </Link>
//               <button
//                 className="button delete"
//                 onClick={() => handleDelete(event.id)}
//               >
//                 Delete
//               </button>
//             </li>
//           ))
//         )}
//       </ul>
//     </div>
//   );
// };

// export default EventList;



import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import "./eventroutes.css";

const EventList = () => {
  const [events, setEvents] = useState([]);
  const [formData, setFormData] = useState({ name: "", date: "" });
  const [search, setSearch] = useState({ name: "", date: "" });

  // Fetch events from backend
  const fetchEvents = async (query = {}) => {
    try {
      let queryString = new URLSearchParams(query).toString();
      const res = await fetch(
        `http://localhost:5000/events${queryString ? `?${queryString}` : ""}`
      );
      if (!res.ok) throw new Error("Failed to fetch events");
      const data = await res.json();
      setEvents(Array.isArray(data) ? data : []);
    } catch (err) {
      console.error("Error fetching events:", err);
      setEvents([]);
    }
  };

  useEffect(() => {
    fetchEvents(); // load all initially
  }, []);

  // Handle Add form input change
  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  // Handle Search input change
  const handleSearchChange = (e) => {
    setSearch({ ...search, [e.target.name]: e.target.value });
  };

  // Add new event
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const res = await fetch("http://localhost:5000/events", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });
      if (!res.ok) throw new Error("Failed to add event");

      setFormData({ name: "", date: "" });
      fetchEvents();
    } catch (err) {
      console.error("Error adding event:", err);
      alert("Failed to add event.");
    }
  };

  // Delete event
  const handleDelete = async (id) => {
    if (!window.confirm("Delete this event?")) return;
    try {
      const res = await fetch(`http://localhost:5000/events/${id}`, {
        method: "DELETE",
      });
      if (!res.ok) throw new Error(await res.text());
      const deleted = await res.json();
      setEvents((prev) => prev.filter((e) => e.id !== deleted.id));
    } catch (err) {
      console.error("Error deleting event:", err);
      alert("Failed to delete event.");
    }
  };

  // Search events
  const handleSearchSubmit = (e) => {
    e.preventDefault();
    fetchEvents(search);
  };

  return (
    <div>
      <h1>Upcoming Events</h1>

      {/* 🔎 Search Form */}
      <form onSubmit={handleSearchSubmit} style={{ marginBottom: "20px" }}>
        <input
          type="text"
          name="name"
          placeholder="Search by name"
          value={search.name}
          onChange={handleSearchChange}
        />
        <input
          type="date"
          name="date"
          value={search.date}
          onChange={handleSearchChange}
        />
        <button type="submit">Search</button>
        <button
          type="button"
          onClick={() => {
            setSearch({ name: "", date: "" });
            fetchEvents();
          }}
        >
          Reset
        </button>
      </form>

      {/* ➕ Add Event Form */}
      <form onSubmit={handleSubmit}>
        <input
          type="text"
          name="name"
          placeholder="Event Name"
          value={formData.name}
          onChange={handleChange}
          required
        />
        <input
          type="date"
          name="date"
          value={formData.date}
          onChange={handleChange}
          required
        />
        <button type="submit">Add Event</button>
      </form>

      {/* 📋 Event List */}
      <ul className="list">
        {events.length === 0 ? (
          <p>No events available.</p>
        ) : (
          events.map((event) => (
            <li key={event.id} className="listItem">
              <strong>{event.name}</strong> - {event.date}
              <Link className="button" to={`/events/update/${event.id}`}>
                Edit
              </Link>
              <button
                className="button delete"
                onClick={() => handleDelete(event.id)}
              >
                Delete
              </button>
            </li>
          ))
        )}
      </ul>
    </div>
  );
};

export default EventList;
